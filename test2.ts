function objectToQueryString(obj: Record<string, any>): string {
  const params = new URLSearchParams();

  for (const key in obj) {
    if (obj.hasOwnProperty(key)) {
      const value = obj[key];

      // Handle nested objects (e.g., JSON strings like `variables`)
      if (typeof value === 'object') {
        params.append(key, JSON.stringify(value));
      } else {
        params.append(key, value);
      }
    }
  }

  return params.toString();
}

const obj = {
  fb_api_caller_class: 'RelayModern',
  fb_api_req_friendly_name: 'CommentsListComponentsPaginationQuery',
  variables: {
    commentsAfterCount: -1,
    commentsAfterCursor:
      'MToxNzUyNTExMDI5OgF1R0NoktIOv4huEuhKuIAdY3sNNbl3HWUDCR8FSZ0BkWSPo4HW5jH4GuxjPjRpNTYo3bCyhwUeuztlYmXoWzrse2opFMr2hMflSMRWHUZQ4wPb2QgZhu9--lxL_TPQ-WIZG3HPI9AkeD5yzxnyi9UEbdaeHIOuxT9MUSc3EFehyJPSYwucWOxJKQ-WP06chQOzseV0ae0RSlqf6JLPOasLaCq_cHjxtnfxjfklYjHe2uOymS2WsCZhKYcKCVhDyVSE8uN8tuGieL_H',
    commentsBeforeCount: null,
    commentsBeforeCursor: null,
    commentsIntentToken: 'RANKED_UNFILTERED_CHRONOLOGICAL_REPLIES_INTENT_V1',
    feedLocation: 'COMET_MEDIA_VIEWER',
    focusCommentID: null,
    scale: 1,
    useDefaultActor: false,
    id: 'ZmVlZGJhY2s6NzEzNDM2ODU0OTc4NTM2',
  },
  server_timestamps: 'true',
  doc_id: '9994312660685367',
};

function extractCommentData(commentNode: any): any {
  const comment: any = {
    id: commentNode.id || '',
    text: commentNode.body?.text || null,
    author: {
      id: commentNode.author?.id || '',
      name: commentNode.author?.name || commentNode.user?.name || '',
      profilePicture:
        commentNode.author?.profile_picture_depth_0?.uri ||
        commentNode.user?.profile_picture?.uri ||
        null,
    },
    timestamp: commentNode.created_time
      ? new Date(commentNode.created_time * 1000).toISOString()
      : null,
    reactions: [],
    totalReactions: 0,
    replies: [],
    totalReplies: commentNode.feedback?.replies_fields?.total_count || 0,
    attachments: commentNode.attachments || [],
    depth: commentNode.depth || 0,
  };

  // Extract reactions if available
  if (commentNode.feedback?.reaction_count?.count) {
    comment.totalReactions = commentNode.feedback.reaction_count.count;
  }

  if (commentNode.feedback?.top_reactions?.edges) {
    comment.reactions = commentNode.feedback.top_reactions.edges.map(
      (edge: any) => ({
        type: edge.node?.localized_name || edge.node?.key || 'unknown',
        count: edge.reaction_count || 0,
      })
    );
  }

  return comment;
}

fetch('https://www.facebook.com/api/graphql/', {
  headers: {
    accept: '*/*',
    'accept-language': 'en-US,en;q=0.9',
    'cache-control': 'no-cache',
    'content-type': 'application/x-www-form-urlencoded',
    pragma: 'no-cache',
    priority: 'u=1, i',
    'sec-ch-prefers-color-scheme': 'dark',
    'sec-ch-ua':
      '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
    'sec-ch-ua-full-version-list':
      '"Not)A;Brand";v="8.0.0.0", "Chromium";v="138.0.7204.100", "Google Chrome";v="138.0.7204.100"',
    'sec-ch-ua-mobile': '?0',
    'sec-ch-ua-model': '""',
    'sec-ch-ua-platform': '"Linux"',
    'sec-ch-ua-platform-version': '"6.11.0"',
    'sec-fetch-dest': 'empty',
    'sec-fetch-mode': 'cors',
    'sec-fetch-site': 'same-origin',
    'x-asbd-id': '359341',
    'x-fb-friendly-name': 'CommentsListComponentsPaginationQuery',
    'x-fb-lsd': 'AVrEqCvTOss',
  },
  referrer:
    'https://www.facebook.com/photo/?fbid=713436824978539&set=a.559526800369543',
  body: objectToQueryString(obj),
  method: 'POST',
})
  .then((res) => res.json())
  .then((data) => {
    const rawComments =
      data.data.node.comment_rendering_instance_for_feed_location.comments;
    console.log(rawComments.page_info);
    const comments = rawComments.edges.map((edge: any) =>
      extractCommentData(edge.node)
    );
    console.log(comments);
  });
