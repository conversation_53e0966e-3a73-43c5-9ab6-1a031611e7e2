import fs from 'fs/promises';
import path from 'path';
import NodeFetchCache, { FileSystemCache } from 'node-fetch-cache';

// ============================================================================
// CONFIGURATION & SETUP
// ============================================================================

const CONFIG = {
  CACHE_DIR: 'fb_cache',
  CACHE_TTL: 1000 * 60 * 60 * 24 * 7, // 7 days
  OUTPUT_DIR: 'output',
  MAX_POST_PAGES: 5,
  MAX_COMMENT_PAGES: 50,
  REQUEST_DELAY: 500, // ms between requests
  USER_ID: '100089366829069', // Facebook user ID to scrape
};

const fetch = NodeFetchCache.create({
  cache: new FileSystemCache({
    cacheDirectory: CONFIG.CACHE_DIR,
    ttl: CONFIG.CACHE_TTL,
  }),
  shouldCacheResponse: (response) => response.ok,
});

// ============================================================================
// TYPES & INTERFACES
// ============================================================================

interface CommentAuthor {
  id: string;
  name: string;
  profilePicture: string | null;
}

interface CommentReaction {
  type: string;
  count: number;
}

interface Comment {
  id: string;
  text: string | null;
  author: CommentAuthor;
  timestamp: string | null;
  reactions: CommentReaction[];
  totalReactions: number;
  replies: Comment[];
  totalReplies: number;
  attachments: any[];
  depth: number;
}

interface PostData {
  postId: string;
  author: {
    name: string;
    id: string;
    url: string;
    profilePicture: string | null;
  };
  postText: string | null;
  sharedPostText: string | null;
  sharedPostInformation: any;
  attachments: any[];
  timestamp: string | null;
  postUrl: string | null;
  reactions: Record<string, any>;
  totalComments: number;
  totalShares: number;
  otherImportantInfo: Record<string, any>;
  feedbackId: string;
  comments?: Comment[];
  commentsCount?: number;
}

interface ScrapingStats {
  totalPosts: number;
  postsWithComments: number;
  totalComments: number;
  averageCommentsPerPost: number;
  processingTime: number;
  cacheHits: number;
  apiCalls: number;
}

// ============================================================================
// UTILITY FUNCTIONS
// ============================================================================

async function ensureDirectoryExists(dirPath: string): Promise<void> {
  try {
    await fs.access(dirPath);
  } catch {
    await fs.mkdir(dirPath, { recursive: true });
  }
}

async function cleanupOldFiles(): Promise<void> {
  console.log('🧹 Cleaning up old files...');

  const filesToClean = [
    'raw_page*.json',
    'posts_page*.json',
    'comments_*.json',
    'test.json',
  ];

  for (const pattern of filesToClean) {
    try {
      const files = await fs.readdir('.');
      const matchingFiles = files.filter((file) => {
        if (pattern.includes('*')) {
          const regex = new RegExp(pattern.replace('*', '.*'));
          return regex.test(file);
        }
        return file === pattern;
      });

      for (const file of matchingFiles) {
        await fs.unlink(file);
        console.log(`   ✓ Removed ${file}`);
      }
    } catch (error) {
      // Ignore errors for files that don't exist
    }
  }
}

function objectToQueryString(obj: Record<string, any>): string {
  const params = new URLSearchParams();
  for (const key in obj) {
    if (obj.hasOwnProperty(key)) {
      const value = obj[key];
      if (typeof value === 'object') {
        params.append(key, JSON.stringify(value));
      } else {
        params.append(key, value);
      }
    }
  }
  return params.toString();
}

function extractAttachments(node: any): any[] {
  const attachments = [];
  const attachmentsToProcess = Array.isArray(node)
    ? node
    : Array.isArray(node?.attachments)
    ? node.attachments
    : [];

  for (const att of attachmentsToProcess) {
    const info: any = {};

    if (att.media && att.media.__typename !== 'GenericAttachmentMedia') {
      info.type = att.media.__typename;
      info.id = att.media.id;
    }

    const photo = att?.styles?.attachment?.media?.photo_image;
    if (photo?.uri) {
      info.uri = photo.uri;
      info.height = photo.height;
      info.width = photo.width;
    }

    if (Object.keys(info).length > 0) attachments.push(info);
  }

  return attachments;
}

// ============================================================================
// POST EXTRACTION FUNCTIONS
// ============================================================================

function extractFacebookPostData(jsonArray: any[]): PostData[] {
  const posts: PostData[] = [];

  jsonArray.forEach((dataBlob) => {
    const edges = dataBlob?.data?.node?.timeline_list_feed_units?.edges || [];

    for (const edge of edges) {
      const node = edge.node;

      const post: PostData = {
        postId: node.post_id,
        author: node.actors?.[0]
          ? {
              name: node.actors[0].name,
              id: node.actors[0].id,
              url: node.actors[0].url,
              profilePicture: node.actors[0].profile_picture?.uri || null,
            }
          : {
              name: '',
              id: '',
              url: '',
              profilePicture: null,
            },
        postText:
          node.message?.text ||
          node?.comet_sections?.content?.story?.comet_sections?.message?.story
            ?.message?.text ||
          null,
        sharedPostText: null,
        sharedPostInformation: null,
        attachments: [],
        timestamp: null,
        postUrl: node?.comet_sections?.content?.story?.wwwURL || null,
        reactions: {},
        totalComments: 0,
        totalShares: 0,
        otherImportantInfo: {},
        feedbackId: node?.feedback?.id,
      };

      // Shared post info
      const shared = node.attached_story;
      if (shared) {
        post.sharedPostInformation = {
          postId: shared.post_id,
          url: shared?.comet_sections?.content?.story?.wwwURL || null,
          author: shared?.actors?.[0]?.name || null,
          text:
            shared?.comet_sections?.content?.story?.comet_sections?.message
              ?.story?.message?.text || null,
          attachments: extractAttachments(
            shared?.comet_sections?.content?.story?.attachments
          ),
        };
      }

      if (node.attachments?.length) {
        post.attachments = extractAttachments(node);
      }

      const ts =
        node.timestamp?.story?.creation_time ||
        node?.comet_sections?.context_layout?.story?.comet_sections?.metadata?.find(
          (m: any) =>
            m.__typename === 'CometFeedStoryMinimizedTimestampStrategy' &&
            m.story?.creation_time
        )?.story?.creation_time;
      post.timestamp = ts ? new Date(ts * 1000).toISOString() : null;

      // Feedback
      const feedback =
        node?.comet_sections?.feedback?.story?.story_ufi_container?.story
          ?.feedback_context?.feedback_target_with_context
          ?.comet_ufi_summary_and_actions_renderer?.feedback ||
        node?.feedback?.story?.feedback_context?.feedback_target_with_context
          ?.comet_ufi_summary_and_actions_renderer?.feedback;

      if (feedback) {
        post.reactions.totalReactions = feedback.reaction_count?.count || 0;

        for (const r of feedback.top_reactions?.edges || []) {
          if (r.node?.localized_name) {
            post.reactions[r.node.localized_name.toLowerCase()] =
              r.reaction_count;
          }
        }

        post.totalComments =
          feedback?.comments_count_summary_renderer?.feedback
            ?.comment_rendering_instance?.comments?.total_count || 0;

        post.totalShares = feedback.share_count?.count || 0;
      }

      if (node.sponsored_data) {
        post.otherImportantInfo.isSponsored = true;
      }

      posts.push(post);
    }
  });

  return posts;
}

async function fetchAndExtractFacebookData(
  cursor: string | null = null,
  requestCount: number = 1,
  maxRequests: number = CONFIG.MAX_POST_PAGES
): Promise<PostData[]> {
  if (requestCount > maxRequests) {
    console.log(`📄 Reached post request limit (${maxRequests})`);
    return [];
  }

  console.log(`📄 Fetching posts page ${requestCount}/${maxRequests}...`);

  const data = {
    fb_api_caller_class: 'RelayModern',
    fb_api_req_friendly_name: 'ProfileCometTimelineFeedRefetchQuery',
    variables: JSON.stringify({
      count: 1,
      cursor,
      feedLocation: 'TIMELINE',
      feedbackSource: 0,
      id: CONFIG.USER_ID,
      privacySelectorRenderLocation: 'COMET_STREAM',
      renderLocation: 'timeline',
      scale: 1,
      stream_count: 1,
    }),
    server_timestamps: 'false',
    doc_id: '9455104801257585',
  };

  const formData = new URLSearchParams();
  for (const [key, val] of Object.entries(data)) {
    formData.append(key, val);
  }

  try {
    const response = await fetch('https://www.facebook.com/api/graphql/', {
      method: 'POST',
      headers: {
        accept: '*/*',
        'content-type': 'application/x-www-form-urlencoded',
        'x-fb-friendly-name': 'ProfileCometTimelineFeedRefetchQuery',
        'x-fb-lsd': 'AVpulFPjB1U',
      },
      body: formData.toString(),
    });

    if (!response.ok) {
      console.error(`❌ HTTP error! status: ${response.status}`);
      return [];
    }

    const lines = (await response.text()).split('\n').filter(Boolean);
    const jsonObjects: any[] = [];

    let nextCursor: string | null = null;

    for (const line of lines) {
      try {
        const json = JSON.parse(line);
        jsonObjects.push(json);

        const endCursor =
          json.data?.page_info?.end_cursor ||
          json.data?.node?.timeline_list_feed_units?.page_info?.end_cursor;
        if (endCursor) nextCursor = endCursor;
      } catch (err) {
        console.error('Parse error:', err);
      }
    }

    // Save raw data for debugging
    await fs.writeFile(
      path.join(CONFIG.OUTPUT_DIR, `raw_page${requestCount}.json`),
      JSON.stringify(jsonObjects, null, 2)
    );

    const extracted = extractFacebookPostData(jsonObjects);
    console.log(
      `   ✓ Extracted ${extracted.length} posts from page ${requestCount}`
    );

    // Save extracted posts
    await fs.writeFile(
      path.join(CONFIG.OUTPUT_DIR, `posts_page${requestCount}.json`),
      JSON.stringify(extracted, null, 2)
    );

    // Add delay between requests
    if (requestCount < maxRequests && nextCursor) {
      await new Promise((resolve) => setTimeout(resolve, CONFIG.REQUEST_DELAY));
    }

    if (nextCursor && requestCount < maxRequests) {
      const next = await fetchAndExtractFacebookData(
        nextCursor,
        requestCount + 1,
        maxRequests
      );
      return [...extracted, ...next];
    }

    return extracted;
  } catch (err) {
    console.error('❌ Fetch error:', err);
    return [];
  }
}

// ============================================================================
// COMMENT EXTRACTION FUNCTIONS
// ============================================================================

function extractCommentData(commentNode: any): Comment {
  const comment: Comment = {
    id: commentNode.id || '',
    text: commentNode.body?.text || null,
    author: {
      id: commentNode.author?.id || '',
      name: commentNode.author?.name || commentNode.user?.name || '',
      profilePicture:
        commentNode.author?.profile_picture_depth_0?.uri ||
        commentNode.user?.profile_picture?.uri ||
        null,
    },
    timestamp: commentNode.created_time
      ? new Date(commentNode.created_time * 1000).toISOString()
      : null,
    reactions: [],
    totalReactions: 0,
    replies: [],
    totalReplies: commentNode.feedback?.replies_fields?.total_count || 0,
    attachments: commentNode.attachments || [],
    depth: commentNode.depth || 0,
  };

  // Extract reactions if available
  if (commentNode.feedback?.reaction_count?.count) {
    comment.totalReactions = commentNode.feedback.reaction_count.count;
  }

  if (commentNode.feedback?.top_reactions?.edges) {
    comment.reactions = commentNode.feedback.top_reactions.edges.map(
      (edge: any) => ({
        type: edge.node?.localized_name || edge.node?.key || 'unknown',
        count: edge.reaction_count || 0,
      })
    );
  }

  return comment;
}

async function fetchCommentsForPost(
  feedbackId: string,
  maxPages: number = CONFIG.MAX_COMMENT_PAGES
): Promise<Comment[]> {
  const allComments: Comment[] = [];
  const visitedCursors = new Set<string>();
  const commentIds = new Set<string>(); // Track unique comment IDs
  let currentCursor: string | null = null;
  let pageCount = 0;

  console.log(`💬 Fetching comments for feedback ID: ${feedbackId}`);

  while (pageCount < maxPages) {
    // Prevent infinite loops by tracking visited cursors
    if (currentCursor && visitedCursors.has(currentCursor)) {
      console.log(
        `   ⚠️  Cursor already visited: ${currentCursor.substring(0, 50)}...`
      );
      break;
    }

    // Add current cursor to visited set
    if (currentCursor) {
      visitedCursors.add(currentCursor);
    }

    pageCount++;

    const obj = {
      fb_api_caller_class: 'RelayModern',
      fb_api_req_friendly_name: 'CommentListComponentsRootQuery',
      variables: {
        commentsAfterCount: -1,
        commentsAfterCursor: currentCursor,
        commentsBeforeCount: null,
        commentsBeforeCursor: null,
        commentsIntentToken: null,
        feedLocation: 'POST_PERMALINK_DIALOG',
        focusCommentID: null,
        scale: 1,
        useDefaultActor: false,
        id: feedbackId,
      },
      server_timestamps: 'true',
      doc_id: '24269275729371154',
    };

    console.log(
      `   📄 Fetching comments page ${pageCount} with cursor: ${
        currentCursor ? currentCursor.substring(0, 50) + '...' : 'null'
      }`
    );

    try {
      const response = await fetch('https://www.facebook.com/api/graphql/', {
        headers: {
          accept: '*/*',
          'content-type': 'application/x-www-form-urlencoded',
          'sec-fetch-site': 'same-origin',
        },
        body: objectToQueryString(obj),
        method: 'POST',
      });

      if (!response.ok) {
        console.error(`   ❌ HTTP error! status: ${response.status}`);
        break;
      }

      const data: any = await response.json();

      // Save debug data for first page
      if (pageCount === 1) {
        await fs.writeFile(
          path.join(
            CONFIG.OUTPUT_DIR,
            `comments_debug_${feedbackId.replace(/[^a-zA-Z0-9]/g, '_')}.json`
          ),
          JSON.stringify(data, null, 2)
        );
      }

      // Extract comments from this page
      const pageComments: Comment[] = [];
      if (
        data?.data?.node?.comment_rendering_instance_for_feed_location?.comments
          ?.edges
      ) {
        const edges =
          data.data.node.comment_rendering_instance_for_feed_location.comments
            .edges;

        for (const edge of edges) {
          const comment = extractCommentData(edge.node);

          // Only add if we haven't seen this comment ID before
          if (!commentIds.has(comment.id)) {
            commentIds.add(comment.id);
            pageComments.push(comment);
          } else {
            console.log(`   ⚠️  Duplicate comment detected: ${comment.id}`);
          }
        }
      }

      console.log(
        `   ✓ Found ${pageComments.length} new comments on page ${pageCount} (${commentIds.size} total unique)`
      );
      allComments.push(...pageComments);

      // Check if there are more comments to fetch
      const pageInfo =
        data?.data?.node?.comment_rendering_instance_for_feed_location?.comments
          ?.page_info;

      console.log(`   📊 Page info:`, {
        hasNextPage: pageInfo?.has_next_page,
        endCursor: pageInfo?.end_cursor
          ? pageInfo.end_cursor.substring(0, 50) + '...'
          : null,
        totalUniqueComments: commentIds.size,
      });

      // Check if we should continue
      if (
        !pageInfo?.has_next_page ||
        !pageInfo?.end_cursor ||
        visitedCursors.has(pageInfo.end_cursor)
      ) {
        console.log(`   🏁 No more comment pages to fetch`);
        break;
      }

      // Set up for next iteration
      currentCursor = pageInfo.end_cursor;

      // Add delay between requests
      await new Promise((resolve) => setTimeout(resolve, CONFIG.REQUEST_DELAY));
    } catch (error) {
      console.error(`   ❌ Error fetching comments page ${pageCount}:`, error);
      break;
    }
  }

  if (pageCount >= maxPages) {
    console.log(`   ⚠️  Reached maximum comment pages limit (${maxPages})`);
  }

  console.log(
    `   📈 Total unique comments fetched: ${allComments.length} across ${pageCount} pages`
  );
  return allComments;
}

// ============================================================================
// MAIN SCRAPING ORCHESTRATION
// ============================================================================

async function scrapePostsWithComments(): Promise<PostData[]> {
  const startTime = Date.now();

  console.log('🚀 Starting Facebook scraper...');
  console.log(`📋 Configuration:
   - Max post pages: ${CONFIG.MAX_POST_PAGES}
   - Max comment pages per post: ${CONFIG.MAX_COMMENT_PAGES}
   - Request delay: ${CONFIG.REQUEST_DELAY}ms
   - Cache directory: ${CONFIG.CACHE_DIR}
   - Output directory: ${CONFIG.OUTPUT_DIR}
  `);

  // Setup
  await ensureDirectoryExists(CONFIG.OUTPUT_DIR);
  await cleanupOldFiles();

  // Ensure output directory exists again after cleanup
  await ensureDirectoryExists(CONFIG.OUTPUT_DIR);

  // Step 1: Fetch all posts
  console.log('\n📄 STEP 1: Fetching posts...');
  const posts = await fetchAndExtractFacebookData();
  console.log(`✅ Fetched ${posts.length} posts total`);

  if (posts.length === 0) {
    console.log('❌ No posts found. Exiting.');
    return [];
  }

  // Save posts data
  await fs.writeFile(
    path.join(CONFIG.OUTPUT_DIR, 'all_facebook_posts.json'),
    JSON.stringify(posts, null, 2)
  );

  // Step 2: Fetch comments for each post
  console.log('\n💬 STEP 2: Fetching comments for each post...');
  const postsWithComments: PostData[] = [];

  for (let i = 0; i < posts.length; i++) {
    const post = posts[i];

    if (!post.feedbackId) {
      console.log(
        `⏭️  Skipping post ${i + 1}/${posts.length} (${
          post.postId
        }) - no feedbackId`
      );
      postsWithComments.push({ ...post, comments: [], commentsCount: 0 });
      continue;
    }

    console.log(
      `\n📝 Processing post ${i + 1}/${posts.length}: ${post.postId}`
    );
    console.log(`   Feedback ID: ${post.feedbackId}`);
    console.log(`   Expected comments: ${post.totalComments}`);

    try {
      const comments = await fetchCommentsForPost(post.feedbackId);

      const postWithComments: PostData = {
        ...post,
        comments,
        commentsCount: comments.length,
      };

      postsWithComments.push(postWithComments);

      // Save individual post with comments
      await fs.writeFile(
        path.join(CONFIG.OUTPUT_DIR, `post_${post.postId}_with_comments.json`),
        JSON.stringify(postWithComments, null, 2)
      );

      console.log(
        `   ✅ Successfully processed post ${post.postId} with ${comments.length} comments`
      );
    } catch (error) {
      console.error(`   ❌ Error processing post ${post.postId}:`, error);
      postsWithComments.push({ ...post, comments: [], commentsCount: 0 });
    }

    // Add delay between posts to avoid rate limiting
    if (i < posts.length - 1) {
      await new Promise((resolve) =>
        setTimeout(resolve, CONFIG.REQUEST_DELAY * 2)
      );
    }
  }

  // Step 3: Save final results
  console.log('\n💾 STEP 3: Saving final results...');

  await fs.writeFile(
    path.join(CONFIG.OUTPUT_DIR, 'complete_posts_with_comments.json'),
    JSON.stringify(postsWithComments, null, 2)
  );

  // Generate and save statistics
  const stats = generateStatistics(postsWithComments, startTime);
  await fs.writeFile(
    path.join(CONFIG.OUTPUT_DIR, 'scraping_statistics.json'),
    JSON.stringify(stats, null, 2)
  );

  console.log('\n📊 SCRAPING COMPLETED!');
  displayStatistics(stats);

  return postsWithComments;
}

function generateStatistics(
  posts: PostData[],
  startTime: number
): ScrapingStats {
  const endTime = Date.now();
  const postsWithComments = posts.filter((p) => (p.comments?.length || 0) > 0);
  const totalComments = posts.reduce(
    (sum, p) => sum + (p.comments?.length || 0),
    0
  );

  return {
    totalPosts: posts.length,
    postsWithComments: postsWithComments.length,
    totalComments,
    averageCommentsPerPost: posts.length > 0 ? totalComments / posts.length : 0,
    processingTime: endTime - startTime,
    cacheHits: 0, // TODO: Implement cache hit tracking
    apiCalls: 0, // TODO: Implement API call tracking
  };
}

function displayStatistics(stats: ScrapingStats): void {
  console.log(`
📈 SCRAPING STATISTICS:
   • Total Posts: ${stats.totalPosts}
   • Posts with Comments: ${stats.postsWithComments}
   • Total Comments: ${stats.totalComments}
   • Average Comments per Post: ${stats.averageCommentsPerPost.toFixed(2)}
   • Processing Time: ${(stats.processingTime / 1000).toFixed(2)} seconds
   • Cache Directory: ${CONFIG.CACHE_DIR}
   • Output Directory: ${CONFIG.OUTPUT_DIR}
  `);
}

// ============================================================================
// ERROR HANDLING & CLEANUP
// ============================================================================

async function handleError(error: any): Promise<void> {
  console.error('\n❌ SCRAPING FAILED!');
  console.error('Error details:', error);

  // Try to save partial results if available
  try {
    const partialResultsPath = path.join(
      CONFIG.OUTPUT_DIR,
      'partial_results_error.json'
    );
    await fs.writeFile(
      partialResultsPath,
      JSON.stringify(
        {
          error: error.message,
          timestamp: new Date().toISOString(),
          config: CONFIG,
        },
        null,
        2
      )
    );
    console.log(`💾 Partial results saved to: ${partialResultsPath}`);
  } catch (saveError) {
    console.error('Failed to save partial results:', saveError);
  }
}

// ============================================================================
// MAIN EXECUTION
// ============================================================================

async function main(): Promise<void> {
  try {
    console.log('🎯 Facebook Post & Comment Scraper v2.0');
    console.log('==========================================\n');

    const results = await scrapePostsWithComments();

    if (results.length > 0) {
      console.log(
        `\n🎉 SUCCESS! Scraped ${results.length} posts with comments.`
      );
      console.log(`📁 Results saved in: ${CONFIG.OUTPUT_DIR}/`);
      console.log(
        `📊 Statistics: ${CONFIG.OUTPUT_DIR}/scraping_statistics.json`
      );
      console.log(
        `📄 Complete data: ${CONFIG.OUTPUT_DIR}/complete_posts_with_comments.json`
      );
    } else {
      console.log(
        '\n⚠️  No results obtained. Check your configuration and network connection.'
      );
    }
  } catch (error) {
    await handleError(error);
    process.exit(1);
  }
}

// Run the scraper
if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch(async (error) => {
    await handleError(error);
    process.exit(1);
  });
}
